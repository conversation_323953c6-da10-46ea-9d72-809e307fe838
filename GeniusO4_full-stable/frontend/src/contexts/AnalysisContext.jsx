import React, { createContext, useContext, useState } from 'react'

const AnalysisContext = createContext()

export const useAnalysis = () => {
  const context = useContext(AnalysisContext)
  if (!context) {
    throw new Error('useAnalysis must be used within an AnalysisProvider')
  }
  return context
}

export const AnalysisProvider = ({ children }) => {
  const [rawApiResponse, setRawApiResponse] = useState(null)
  const [analysis, setAnalysis] = useState(null)
  const [recommendations, setRecommendations] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  const updateAnalysisData = (data) => {
    setRawApiResponse(data.rawApiResponse || null)
    setAnalysis(data.analysis || null)
    setRecommendations(data.recommendations || null)
    setIsLoading(data.isLoading || false)
  }

  const value = {
    rawApiResponse,
    analysis,
    recommendations,
    isLoading,
    setRawApiResponse,
    setAnalysis,
    setRecommendations,
    setIsLoading,
    updateAnalysisData
  }

  return (
    <AnalysisContext.Provider value={value}>
      {children}
    </AnalysisContext.Provider>
  )
}

export default AnalysisContext
