@import "tailwindcss";
@import "./styles/modernEffects.css";

@theme {
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-secondary: #6b7280;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-card: #1e293b;
  --color-border: #334155;
  --color-text: #f1f5f9;
  --color-text-muted: #94a3b8;
  --color-foreground: #f1f5f9;
  --color-muted-foreground: #94a3b8;
  --color-primary-foreground: #ffffff;
  --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Базовые стили для темной темы */
body {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: var(--color-foreground);
  background-color: var(--color-background);
}

/* Tailwind CSS переменные для темной темы */
:root {
  --background: 15 23 42; /* slate-900 */
  --foreground: 241 245 249; /* slate-100 */
  --card: 30 41 59; /* slate-800 */
  --card-foreground: 241 245 249; /* slate-100 */
  --popover: 30 41 59; /* slate-800 */
  --popover-foreground: 241 245 249; /* slate-100 */
  --primary: 37 99 235; /* blue-600 */
  --primary-foreground: 255 255 255; /* white */
  --secondary: 51 65 85; /* slate-700 */
  --secondary-foreground: 241 245 249; /* slate-100 */
  --muted: 51 65 85; /* slate-700 */
  --muted-foreground: 148 163 184; /* slate-400 */
  --accent: 51 65 85; /* slate-700 */
  --accent-foreground: 241 245 249; /* slate-100 */
  --destructive: 239 68 68; /* red-500 */
  --destructive-foreground: 255 255 255; /* white */
  --border: 51 65 85; /* slate-700 */
  --input: 51 65 85; /* slate-700 */
  --ring: 37 99 235; /* blue-600 */
}

