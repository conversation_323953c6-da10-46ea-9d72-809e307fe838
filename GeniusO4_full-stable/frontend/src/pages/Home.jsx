import React, { useState, useEffect } from 'react';

// Ленивая загрузка компонентов для избежания проблем с импортами
const TradingViewChart = React.lazy(() => import('../TradingViewChart'));
const OverlayControls = React.lazy(() => import('../components/OverlayControls'));

// Импорт реальных данных для тестирования
// import rawApiResponseData from '../../../BASE/data/chatgpt_response_1749154674.json';

// Загрузка данных из public папки
const loadRealTestData = async () => {
  try {
    const response = await fetch('/chatgpt_response_1749154674.json');
    return await response.json();
  } catch (error) {
    console.error('Ошибка загрузки тестовых данных:', error);
    return null;
  }
};

// Простой компонент для отображения анализа
function AnalysisSections({ analysis, activeLayers = [] }) {
  if (!analysis) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>Нет данных анализа</p>
        <p className="text-sm mt-2">Запустите анализ для получения результатов</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Результаты анализа</h3>

      {Object.entries(analysis).map(([key, value]) => {
        if (!value) return null;

        return (
          <div key={key} className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2 capitalize">
              {key.replace(/_/g, ' ')}
            </h4>
            <div className="text-sm text-gray-600">
              {typeof value === 'string' ? (
                <p className="whitespace-pre-wrap">{value}</p>
              ) : typeof value === 'object' ? (
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(value, null, 2)}
                </pre>
              ) : (
                <p>{String(value)}</p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

function Home() {
  // Убираем Redux пока что для упрощения
  // const token = useSelector((state) => state.auth.token);

  const [symbol,  setSymbol]  = useState('BTCUSDT');
  const [interval,setInterval]= useState('4h');
  const [limit,   setLimit]   = useState(144);

  // Состояние для реальных данных тестирования
  const [useRealData, setUseRealData] = useState(true);
  const [realApiResponse, setRealApiResponse] = useState(null);
  const [layers,  setLayers]  = useState(['RSI']);
  const [data,    setData]    = useState([]);
  const [analysis,setAnalysis]= useState(null);
  const [overlaySettings, setOverlaySettings] = useState({
    showSupportResistance: true,
    showTrendLines: true,
    showFibonacci: true,
    showImbalances: true,
    // ЭТАП 1: Критически важные индикаторы
    showPsychologicalLevels: true,
    showPivotPoints: true,
    showCandlestickPatterns: true,
    // ЭТАП 2: Структурный анализ
    showUnfinishedZones: true,
    showStructuralEdge: true,
    showDivergenceAnalysis: true
  });
  const [available,setAvailable]= useState([]);
  const [loading, setLoading] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Темная тема по умолчанию
  const [focusedSection, setFocusedSection] = useState(null);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);

  // Загрузка реальных данных для тестирования
  useEffect(() => {
    const loadData = async () => {
      if (useRealData) {
        const data = await loadRealTestData();
        if (data) {
          setRealApiResponse(data);
          console.log('Загружены реальные данные для тестирования:', data);
        }
      }
    };

    loadData();
  }, [useRealData]);

  const toggleLayer = (name) =>
    setLayers((prev) =>
      prev.includes(name) ? prev.filter((l) => l !== name) : [...prev, name]);

  // Обработчик быстрых действий
  const handleQuickAction = (actionId) => {
    switch (actionId) {
      case 'view_recommendations':
        setFocusedSection('recommendations');
        // Прокрутка к секции рекомендаций
        document.querySelector('[data-section="recommendations"]')?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        break;
      case 'view_prediction':
        setFocusedSection('price_prediction');
        document.querySelector('[data-section="price_prediction"]')?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        break;
      case 'export_analysis':
        if (analysis) {
          const dataStr = JSON.stringify(analysis, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `analysis_${symbol}_${new Date().toISOString().split('T')[0]}.json`;
          link.click();
          URL.revokeObjectURL(url);
        }
        break;
      case 'share_analysis':
        if (analysis && navigator.share) {
          navigator.share({
            title: `Анализ ${symbol}`,
            text: `Торговый анализ для ${symbol}`,
            url: window.location.href
          });
        } else if (analysis) {
          // Fallback для браузеров без Web Share API
          navigator.clipboard.writeText(window.location.href);
        }
        break;
      default:
        console.log('Неизвестное действие:', actionId);
    }
  };

  const handleSectionFocus = (sectionId) => {
    setFocusedSection(sectionId);
  };

  const loadTestData = async () => {
    try {
      // Демо-данные для тестирования визуальных эффектов
      const demoAnalysis = {
        recommendations: {
          action: "BUY",
          confidence: 0.85,
          reasons: [
            "Сильный восходящий тренд",
            "RSI показывает перепроданность",
            "Объем торгов увеличивается"
          ],
          target_price: 45000,
          stop_loss: 42000
        },
        price_prediction: {
          next_24h: 44500,
          next_7d: 46000,
          confidence: 0.78,
          trend: "bullish"
        },
        risk_assessment: {
          level: "medium",
          score: 0.6,
          factors: ["Высокая волатильность", "Макроэкономические риски"]
        },
        technical_indicators: {
          RSI: 35.2,
          MACD: 0.15,
          BB_position: "lower"
        }
      };

      // Демо-данные для графика (OHLC)
      const demoOhlcData = [
        { time: '2024-01-01', open: 103000, high: 103500, low: 102800, close: 103200 },
        { time: '2024-01-02', open: 103200, high: 103800, low: 103000, close: 103600 },
        { time: '2024-01-03', open: 103600, high: 104200, low: 103400, close: 103900 },
        { time: '2024-01-04', open: 103900, high: 104500, low: 103700, close: 104200 },
        { time: '2024-01-05', open: 104200, high: 104800, low: 104000, close: 104500 },
        { time: '2024-01-06', open: 104500, high: 105000, low: 104300, close: 104700 },
        { time: '2024-01-07', open: 104700, high: 105200, low: 104500, close: 104900 },
        { time: '2024-01-08', open: 104900, high: 105500, low: 104700, close: 105200 },
        { time: '2024-01-09', open: 105200, high: 105800, low: 105000, close: 105500 },
        { time: '2024-01-10', open: 105500, high: 106000, low: 105300, close: 105800 }
      ];

      setAnalysis(demoAnalysis);
      setData(demoOhlcData);
      setAvailable(['RSI', 'MACD', 'BB']);
    } catch (err) {
      console.error(err);
      alert('Ошибка чтения тестовых данных');
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      console.log('Запуск анализа...', { symbol, interval, limit, layers });

      const body = { symbol, interval, limit, indicators: layers };
      const headers = { 'Content-Type': 'application/json' };
      if (token) headers.Authorization = `Bearer ${token}`;

      const res = await fetch('/api/analyze', {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      console.log('Ответ получен:', res.status, res.statusText);

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const json = await res.json();
      console.log('Данные анализа:', json);

      setAnalysis(json.analysis);
      setData(json.ohlc || []);
      setAvailable(json.indicators || []);

      console.log('Состояние обновлено');
    } catch (error) {
      console.error('Ошибка при загрузке данных:', error);
      alert('Ошибка при выполнении анализа: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#1a1a1a', color: 'white', padding: '20px' }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {/* Заголовок */}
        <div>
          <h1 style={{ fontSize: '24px', marginBottom: '10px' }}>ChartGenius - Главная страница</h1>
          <p>Система анализа криптовалют с интеграцией TradingView</p>
        </div>

        {/* Кнопка загрузки тестовых данных */}
        <div style={{ padding: '20px', backgroundColor: '#2a2a2a', borderRadius: '8px' }}>
          <h2 style={{ marginBottom: '15px' }}>Управление данными</h2>
          <button
            onClick={loadTestData}
            disabled={loading}
            style={{
              padding: '12px 24px',
              backgroundColor: loading ? '#666' : '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: loading ? 'not-allowed' : 'pointer',
              fontSize: '16px'
            }}
          >
            {loading ? 'Загрузка...' : 'Загрузить тестовые данные'}
          </button>

          <div style={{ marginTop: '10px' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <input
                type="checkbox"
                checked={useRealData}
                  onChange={(e) => setUseRealData(e.target.checked)}
              />
              Использовать реальные данные из JSON
            </label>
            <div style={{ fontSize: '12px', color: '#888', marginTop: '5px' }}>
              {useRealData ? 'Используются реальные данные из JSON' : 'Используются демо-данные'}
            </div>
          </div>
        </div>

        {/* Статус загрузки данных */}
        {realApiResponse && (
          <div style={{ padding: '20px', backgroundColor: '#2a2a2a', borderRadius: '8px' }}>
            <h2 style={{ marginBottom: '15px', color: '#4CAF50' }}>✅ Данные загружены успешно</h2>
            <p>Загружен анализ с {Object.keys(realApiResponse).length} категориями данных</p>
            <div style={{ marginTop: '10px', fontSize: '14px', color: '#ccc' }}>
              Доступные категории: {Object.keys(realApiResponse).join(', ')}
            </div>
          </div>
        )}

        {/* Область для графика */}
        <div style={{ padding: '20px', backgroundColor: '#2a2a2a', borderRadius: '8px', minHeight: '400px' }}>
          <h2 style={{ marginBottom: '15px' }}>График TradingView</h2>
          <React.Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}>Загрузка графика...</div>}>
            {realApiResponse ? (
              <TradingViewChart
                data={data}
                layers={layers}
                analysis={analysis}
                rawApiResponse={realApiResponse}
                overlaySettings={overlaySettings}
              />
            ) : (
              <div style={{
                padding: '40px',
                textAlign: 'center',
                border: '2px dashed #666',
                borderRadius: '8px',
                color: '#888'
              }}>
                <p>Загрузите тестовые данные для отображения графика</p>
              </div>
            )}
          </React.Suspense>
        </div>

        {/* Управление наложениями */}
        {realApiResponse && (
          <div style={{ padding: '20px', backgroundColor: '#2a2a2a', borderRadius: '8px' }}>
            <h2 style={{ marginBottom: '15px' }}>Управление аналитическими наложениями</h2>
            <React.Suspense fallback={<div>Загрузка управления...</div>}>
              <OverlayControls
                overlaySettings={overlaySettings}
                onSettingsChange={setOverlaySettings}
              />
            </React.Suspense>
          </div>
        )}
      </div>
    </div>
  );
}

export default Home;