import React, { useState, useEffect } from 'react';

// Загрузка данных из public папки
const loadTestData = async () => {
  try {
    const response = await fetch('/chatgpt_response_1749154674.json');
    return await response.json();
  } catch (error) {
    console.error('Ошибка загрузки тестовых данных:', error);
    return null;
  }
};

function Home() {
  const [realApiResponse, setRealApiResponse] = useState(null);
  const [loading, setLoading] = useState(true);

  // Загрузка реальных данных для тестирования
  useEffect(() => {
    const loadData = async () => {
      console.log('Загружаем тестовые данные...');
      const data = await loadTestData();
      if (data) {
        setRealApiResponse(data);
        console.log('✅ Данные загружены:', data);
      } else {
        console.error('❌ Не удалось загрузить данные');
      }
      setLoading(false);
    };
    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-8">
        <h1 className="text-4xl font-bold mb-4">🚀 ChartGenius</h1>
        <p className="text-xl">Загружаем данные...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <h1 className="text-4xl font-bold mb-4">🚀 ChartGenius Home</h1>
      <p className="text-xl mb-4">Главная страница загружена!</p>
      
      {realApiResponse ? (
        <div className="mt-8 space-y-4">
          <div className="p-4 bg-green-600 rounded">
            <h2 className="text-xl font-bold mb-2">✅ Данные загружены успешно!</h2>
            <p>Psychological Levels: {realApiResponse.psychological_levels?.levels?.length || 0} уровней</p>
            <p>Support/Resistance: {(realApiResponse.support_resistance_levels?.supports?.length || 0) + (realApiResponse.support_resistance_levels?.resistances?.length || 0)} уровней</p>
            <p>Pivot Points: {realApiResponse.pivot_points ? 'Есть' : 'Нет'}</p>
            <p>Candlestick Patterns: {realApiResponse.candlestick_patterns?.length || 0} паттернов</p>
          </div>
          
          <div className="p-4 bg-blue-600 rounded">
            <h3 className="text-lg font-bold mb-2">🎯 ЭТАП 1: Критически важные индикаторы</h3>
            <ul className="space-y-1">
              <li>✅ Задача 1: Psychological Levels - ИСПРАВЛЕНО</li>
              <li>🔄 Задача 2: Pivot Points - В РАБОТЕ</li>
              <li>⏳ Задача 3: Candlestick Patterns - ОЖИДАЕТ</li>
            </ul>
          </div>
          
          <div className="p-4 bg-purple-600 rounded">
            <h3 className="text-lg font-bold mb-2">📊 Данные для тестирования:</h3>
            <div className="text-sm space-y-1">
              <p><strong>Psychological Levels:</strong></p>
              {realApiResponse.psychological_levels?.levels?.map((level, index) => (
                <div key={index} className="ml-4">
                  • {level.level} ({level.type}) - {level.date}
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-8 p-4 bg-red-600 rounded">
          <h2 className="text-xl font-bold mb-2">❌ Ошибка загрузки данных</h2>
          <p>Не удалось загрузить тестовые данные из JSON файла</p>
        </div>
      )}
    </div>
  );
}

export default Home;
