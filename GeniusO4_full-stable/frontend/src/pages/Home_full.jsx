import React, { useState, useEffect } from 'react';
// import TradingViewChart from '../TradingViewChart';
// import ControlPanel from '../components/ControlPanel';
// import OverlayControls from '../components/OverlayControls';
// import ChartToolbar from '../components/ChartToolbar';

// Загрузка данных из public папки
const loadTestData = async () => {
  try {
    const response = await fetch('/chatgpt_response_1749154674.json');
    return await response.json();
  } catch (error) {
    console.error('Ошибка загрузки тестовых данных:', error);
    return null;
  }
};

function Home() {
  // Основные состояния
  const [symbol, setSymbol] = useState('BTCUSDT');
  const [interval, setInterval] = useState('4h');
  const [limit, setLimit] = useState(144);
  const [layers, setLayers] = useState(['RSI']);
  const [data, setData] = useState([]);
  const [analysis, setAnalysis] = useState(null);
  const [available, setAvailable] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);

  // Состояние для реальных данных тестирования
  const [useRealData, setUseRealData] = useState(true);
  const [realApiResponse, setRealApiResponse] = useState(null);

  // Настройки оверлеев с новыми индикаторами ЭТАП 1 и ЭТАП 2
  const [overlaySettings, setOverlaySettings] = useState({
    showSupportResistance: true,
    showTrendLines: true,
    showFibonacci: true,
    showImbalances: true,
    // ЭТАП 1: Критически важные индикаторы
    showPsychologicalLevels: true,
    showPivotPoints: true,
    showCandlestickPatterns: true,
    // ЭТАП 2: Структурный анализ
    showUnfinishedZones: true,
    showStructuralEdge: true,
    showDivergenceAnalysis: true
  });

  // Загрузка реальных данных для тестирования
  useEffect(() => {
    const loadData = async () => {
      if (useRealData) {
        console.log('🔄 Загружаем реальные данные для тестирования...');
        const data = await loadTestData();
        if (data) {
          setRealApiResponse(data);
          setAnalysis(data);
          console.log('✅ Реальные данные загружены:', data);
          
          // Генерируем демо OHLC данные для графика
          const demoOhlcData = generateDemoOhlcData();
          setData(demoOhlcData);
          setAvailable(['RSI', 'MACD', 'BB']);
        } else {
          console.error('❌ Не удалось загрузить реальные данные');
        }
      }
    };
    loadData();
  }, [useRealData]);

  // Генерация демо OHLC данных для графика
  const generateDemoOhlcData = () => {
    const basePrice = 104000;
    const data = [];
    const startDate = new Date('2025-01-01');
    
    for (let i = 0; i < 50; i++) {
      const date = new Date(startDate);
      date.setHours(date.getHours() + i * 4); // 4-часовые свечи
      
      const variation = (Math.random() - 0.5) * 2000; // ±1000
      const open = basePrice + variation + i * 20; // небольшой восходящий тренд
      const high = open + Math.random() * 800;
      const low = open - Math.random() * 800;
      const close = low + Math.random() * (high - low);
      
      data.push({
        time: date.toISOString().split('T')[0],
        open: Math.round(open),
        high: Math.round(high),
        low: Math.round(low),
        close: Math.round(close)
      });
    }
    
    return data;
  };

  // Переключение слоев
  const toggleLayer = (name) => {
    setLayers((prev) =>
      prev.includes(name) ? prev.filter((l) => l !== name) : [...prev, name]
    );
  };

  // Загрузка данных с сервера (заглушка)
  const loadData = async () => {
    try {
      setLoading(true);
      console.log('🔄 Запуск анализа...', { symbol, interval, limit, layers });
      
      // Имитация загрузки
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Используем реальные данные если они есть
      if (realApiResponse) {
        setAnalysis(realApiResponse);
        const demoOhlcData = generateDemoOhlcData();
        setData(demoOhlcData);
        setAvailable(['RSI', 'MACD', 'BB', 'psychological_levels']);
        console.log('✅ Анализ завершен с реальными данными');
      } else {
        console.log('⚠️ Реальные данные недоступны, используем демо');
      }
    } catch (error) {
      console.error('❌ Ошибка при загрузке данных:', error);
      alert('Ошибка при выполнении анализа: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Загрузка тестовых данных
  const loadTestDataHandler = async () => {
    console.log('🔄 Загрузка тестовых данных...');
    const data = await loadTestData();
    if (data) {
      setRealApiResponse(data);
      setAnalysis(data);
      const demoOhlcData = generateDemoOhlcData();
      setData(demoOhlcData);
      setAvailable(['RSI', 'MACD', 'BB', 'psychological_levels']);
      console.log('✅ Тестовые данные загружены');
    }
  };

  return (
    <div className="pro-theme min-h-screen">
      {/* Трехколоночная структура: [Левая панель 20%] [График 60%] [Правая панель 20%] */}
      <div className="flex h-screen">
        {/* Левая панель - Панель управления (20%) */}
        <div className="w-1/5 min-w-[280px] bg-gray-800 border-r border-gray-700 flex flex-col p-4">
          <h3 className="text-lg font-semibold text-white mb-4">🎛️ Панель управления</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-300 mb-1">Символ:</label>
              <input
                type="text"
                value={symbol}
                onChange={(e) => setSymbol(e.target.value)}
                className="w-full p-2 bg-gray-700 text-white rounded"
              />
            </div>

            <div>
              <label className="block text-sm text-gray-300 mb-1">Интервал:</label>
              <select
                value={interval}
                onChange={(e) => setInterval(e.target.value)}
                className="w-full p-2 bg-gray-700 text-white rounded"
              >
                <option value="1h">1h</option>
                <option value="4h">4h</option>
                <option value="1d">1d</option>
              </select>
            </div>

            <button
              onClick={loadData}
              disabled={loading}
              className="w-full p-2 bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50"
            >
              {loading ? 'Анализ...' : 'Запустить анализ'}
            </button>

            <button
              onClick={loadTestDataHandler}
              className="w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded"
            >
              Загрузить тестовые данные
            </button>
          </div>

          {/* Управление аналитическими наложениями */}
          <div className="mt-6 pt-4 border-t border-gray-700">
            <h4 className="text-sm font-semibold text-white mb-2">📊 Наложения</h4>
            <div className="space-y-2 text-sm">
              {Object.entries(overlaySettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-gray-300">{key.replace('show', '').replace(/([A-Z])/g, ' $1')}</span>
                  <input
                    type="checkbox"
                    checked={value}
                    onChange={(e) => setOverlaySettings({...overlaySettings, [key]: e.target.checked})}
                    className="rounded"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Переключатель реальных данных для тестирования */}
          <div className="p-4 border-t border-pro-border-light">
            <div className="flex items-center justify-between">
              <span className="text-xs text-pro-text-secondary">Тестовые данные</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={useRealData}
                  onChange={(e) => setUseRealData(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-9 h-5 bg-pro-bg-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-pro-success"></div>
              </label>
            </div>
            <div className="text-xs text-pro-text-muted mt-1">
              {useRealData ? 'Используются реальные данные из JSON' : 'Используются демо-данные'}
            </div>
          </div>

          {/* Статус индикаторов ЭТАП 1 и ЭТАП 2 */}
          <div className="p-4 border-t border-pro-border-light">
            <h4 className="text-sm font-semibold mb-2 text-pro-text-primary">🎯 ЭТАП 1: Критические</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Psychological Levels</span>
                <span className="text-green-400">✅ ИСПРАВЛЕНО</span>
              </div>
              <div className="flex justify-between">
                <span>Pivot Points</span>
                <span className="text-yellow-400">🔄 В РАБОТЕ</span>
              </div>
              <div className="flex justify-between">
                <span>Candlestick Patterns</span>
                <span className="text-gray-400">⏳ ОЖИДАЕТ</span>
              </div>
            </div>
            
            <h4 className="text-sm font-semibold mb-2 mt-4 text-pro-text-primary">📊 ЭТАП 2: Структурный</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Unfinished Zones</span>
                <span className="text-gray-400">⏳ ОЖИДАЕТ</span>
              </div>
              <div className="flex justify-between">
                <span>Structural Edge</span>
                <span className="text-gray-400">⏳ ОЖИДАЕТ</span>
              </div>
              <div className="flex justify-between">
                <span>Divergence Analysis</span>
                <span className="text-gray-400">⏳ ОЖИДАЕТ</span>
              </div>
            </div>
          </div>
        </div>

        {/* Центральная область - График (60%) */}
        <div className="flex-1 flex flex-col">
          {/* Панель инструментов графика */}
          <div className="bg-gray-800 border-b border-gray-700 p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-lg font-semibold text-white">{symbol}</span>
                <span className="text-sm text-gray-400">{interval}</span>
                <span className="text-xs text-green-400">● Live</span>
              </div>
              <div className="flex items-center space-x-2">
                <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm">Свечи</button>
                <button className="px-3 py-1 bg-gray-700 text-white rounded text-sm">Развернуть</button>
              </div>
            </div>
          </div>

          {/* Основной контент - График (ЦЕНТРАЛЬНЫЙ ЭЛЕМЕНТ) */}
          <div className="flex-1 p-4">
            <div className="bg-gray-900 rounded-lg border border-gray-700" style={{ height: '600px', minHeight: '400px' }}>
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-4">📈 TradingView График</h3>
                  <p className="text-gray-400 mb-4">График будет здесь после подключения TradingViewChart</p>

                  {realApiResponse && (
                    <div className="bg-green-600 p-4 rounded text-white text-left max-w-md">
                      <h4 className="font-bold mb-2">✅ Данные для визуализации готовы:</h4>
                      <ul className="text-sm space-y-1">
                        <li>• Psychological Levels: {realApiResponse.psychological_levels?.levels?.length || 0} уровней</li>
                        <li>• Support/Resistance: {(realApiResponse.support_resistance_levels?.supports?.length || 0) + (realApiResponse.support_resistance_levels?.resistances?.length || 0)} уровней</li>
                        <li>• Pivot Points: {realApiResponse.pivot_points ? 'Готов' : 'Нет данных'}</li>
                        <li>• Candlestick Patterns: {realApiResponse.candlestick_patterns?.length || 0} паттернов</li>
                        <li>• OHLC данные: {data.length} свечей</li>
                      </ul>
                    </div>
                  )}

                  {!realApiResponse && (
                    <div className="bg-yellow-600 p-4 rounded text-white">
                      <p>Включите "Тестовые данные" для загрузки реальных данных</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Правая панель - Анализ и рекомендации (20%) */}
        <div className="w-1/5 min-w-[320px] pro-bg-secondary pro-border-l p-4">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-pro-text-primary">📈 Анализ данных</h3>
            
            {realApiResponse ? (
              <div className="space-y-3">
                <div className="p-3 bg-green-600 rounded text-sm">
                  <h4 className="font-bold mb-1">✅ Данные загружены</h4>
                  <p>Psychological Levels: {realApiResponse.psychological_levels?.levels?.length || 0}</p>
                  <p>Support/Resistance: {(realApiResponse.support_resistance_levels?.supports?.length || 0) + (realApiResponse.support_resistance_levels?.resistances?.length || 0)}</p>
                  <p>Pivot Points: {realApiResponse.pivot_points ? 'Есть' : 'Нет'}</p>
                  <p>Candlestick Patterns: {realApiResponse.candlestick_patterns?.length || 0}</p>
                </div>
                
                <div className="p-3 bg-blue-600 rounded text-sm">
                  <h4 className="font-bold mb-1">🎯 Прогресс реализации</h4>
                  <p>Текущий: 33% (4/12 категорий)</p>
                  <p>Цель: 100% (12/12 категорий)</p>
                  <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{width: '33%'}}></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-3 bg-red-600 rounded text-sm">
                <h4 className="font-bold mb-1">❌ Данные не загружены</h4>
                <p>Включите переключатель "Тестовые данные" для загрузки реальных данных из JSON файла</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Home;
