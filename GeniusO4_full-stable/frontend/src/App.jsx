import React from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import './index.css'
// import './styles/professionalTheme.css'
import Home from './pages/Home'
// import About from './pages/About'
// import AdminPanel from './pages/AdminPanel'
// import UserDashboard from './pages/UserDashboard'

export default function App() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Простой тест для проверки React */}
      <div className="p-4 bg-blue-600">
        <h1 className="text-2xl font-bold mb-4">🚀 ChartGenius v2.0 - React работает!</h1>
        <p className="mb-4">Загружаем основной интерфейс...</p>
      </div>

      {/* Professional Header - Compact */}
      <header className="bg-gray-800 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-white">ChartGenius</h1>
            <span className="text-xs text-gray-400">v2.0</span>
          </div>
          <nav className="flex items-center space-x-4">
            <Link to="/" className="text-sm text-gray-300 hover:text-white">Главная</Link>
            <Link to="/about" className="text-sm text-gray-300 hover:text-white">О системе</Link>
            <Link to="/dashboard" className="text-sm text-gray-300 hover:text-white">Кабинет</Link>
            <Link to="/admin" className="text-sm text-gray-300 hover:text-white">Админ</Link>
            <div className="flex items-center space-x-2">
              <span className="px-2 py-1 bg-green-600 text-xs rounded">Готов</span>
              <span className="px-2 py-1 bg-blue-600 text-xs rounded">Live</span>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content - Full Height */}
      <main className="p-4">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<div className="p-4 bg-yellow-600 text-white"><h2>О системе</h2></div>} />
          <Route path="/admin" element={<div className="p-4 bg-red-600 text-white"><h2>Админ панель</h2></div>} />
          <Route path="/dashboard" element={<div className="p-4 bg-purple-600 text-white"><h2>Кабинет пользователя</h2></div>} />
        </Routes>
      </main>
    </div>
  )
}
