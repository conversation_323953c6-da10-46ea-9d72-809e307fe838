import React from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import './index.css'
import ErrorBoundary from './components/ErrorBoundary'

// Простая тестовая страница
function TestPage() {
  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1>ChartGenius - Система работает!</h1>
      <p>✅ React загружен и работает корректно</p>
      <button
        onClick={() => alert('Кнопка работает!')}
        style={{
          padding: '10px 20px',
          backgroundColor: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '10px'
        }}
      >
        Тест кнопки
      </button>
      <div style={{ marginTop: '20px' }}>
        <Link to="/home" style={{ color: '#4CAF50', textDecoration: 'none', fontSize: '18px' }}>
          → Перейти к главной странице
        </Link>
      </div>
    </div>
  )
}

// Импортируем Home только когда нужно
const Home = React.lazy(() => import('./pages/Home'))

export default function App() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#1a1a1a' }}>
      <header style={{ padding: '10px 20px', backgroundColor: '#2a2a2a', color: 'white' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ margin: 0, fontSize: '20px' }}>ChartGenius v2.0</h1>
          <nav style={{ display: 'flex', gap: '20px' }}>
            <Link to="/" style={{ color: 'white', textDecoration: 'none' }}>Тест</Link>
            <Link to="/home" style={{ color: 'white', textDecoration: 'none' }}>Главная</Link>
          </nav>
        </div>
      </header>

      <main>
        <ErrorBoundary>
          <React.Suspense fallback={<div style={{ padding: '20px', color: 'white' }}>Загрузка...</div>}>
            <Routes>
              <Route path="/" element={<TestPage />} />
              <Route path="/home" element={<Home />} />
            </Routes>
          </React.Suspense>
        </ErrorBoundary>
      </main>
    </div>
  )
}
