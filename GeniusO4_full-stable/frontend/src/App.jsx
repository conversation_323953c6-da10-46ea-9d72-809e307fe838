import React from 'react'
import { Routes, Route, Link } from 'react-router-dom'
import './index.css'
import ErrorBoundary from './components/ErrorBoundary'
import DesktopLayout from './layouts/DesktopLayout'
import { AnalysisProvider, useAnalysis } from './contexts/AnalysisContext'

// Простая тестовая страница
function TestPage() {
  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1>ChartGenius - Система работает!</h1>
      <p>✅ React загружен и работает корректно</p>
      <button
        onClick={() => alert('Кнопка работает!')}
        style={{
          padding: '10px 20px',
          backgroundColor: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '10px'
        }}
      >
        Тест кнопки
      </button>
      <div style={{ marginTop: '20px' }}>
        <Link to="/home" style={{ color: '#4CAF50', textDecoration: 'none', fontSize: '18px' }}>
          → Перейти к главной странице
        </Link>
      </div>
    </div>
  )
}

// Импортируем Home только когда нужно
const Home = React.lazy(() => import('./pages/Home'))

// Компонент-обертка для Home с контекстом
const HomeWithLayout = () => {
  const analysisContext = useAnalysis()

  return (
    <DesktopLayout
      rightPanelData={{
        rawApiResponse: analysisContext.rawApiResponse,
        recommendations: analysisContext.recommendations,
        isLoading: analysisContext.isLoading
      }}
    >
      <Home />
    </DesktopLayout>
  )
}

export default function App() {
  return (
    <AnalysisProvider>
      <ErrorBoundary>
        <React.Suspense fallback={<div style={{ padding: '20px', color: 'white' }}>Загрузка...</div>}>
          <Routes>
            <Route path="/" element={<TestPage />} />
            <Route path="/home" element={<HomeWithLayout />} />
          </Routes>
        </React.Suspense>
      </ErrorBoundary>
    </AnalysisProvider>
  )
}
