/**
 * Утилита для парсинга реальных данных анализа от модели
 * Обрабатывает JSON ответ и извлекает нужную информацию для компонентов
 */

/**
 * Парсит JSON ответ от ChatGPT API и извлекает данные анализа
 * @param {Object} apiResponse - Полный ответ от API
 * @returns {Object} Обработанные данные анализа
 */
export const parseAnalysisData = (apiResponse) => {
  try {
    // Извлекаем content из структуры API ответа
    const content = apiResponse?.choices?.[0]?.message?.content;
    if (!content) {
      throw new Error('Нет данных в ответе API');
    }

    // Парсим JSON из content
    const analysisData = JSON.parse(content);
    
    return {
      // Основной анализ
      primaryAnalysis: analysisData.primary_analysis || {},
      
      // Уверенность в торговых решениях
      confidence: analysisData.confidence_in_trading_decisions || {},
      
      // Уровни поддержки и сопротивления
      supportResistance: analysisData.support_resistance_levels || {},
      
      // Трендовые линии
      trendLines: analysisData.trend_lines || {},
      
      // Пивот точки
      pivotPoints: analysisData.pivot_points || {},
      
      // Незавершенные зоны
      unfinishedZones: analysisData.unfinished_zones || [],
      
      // Дисбалансы
      imbalances: analysisData.imbalances || [],
      
      // Анализ Фибоначчи
      fibonacciAnalysis: analysisData.fibonacci_analysis || {},
      
      // Волновой анализ Эллиота
      elliottWaveAnalysis: analysisData.elliott_wave_analysis || {},
      
      // Анализ дивергенций
      divergenceAnalysis: analysisData.divergence_analysis || [],
      
      // Структурные преимущества
      structuralEdge: analysisData.structural_edge || [],
      
      // Свечные паттерны
      candlestickPatterns: analysisData.candlestick_patterns || [],
      
      // Анализ индикаторов
      indicatorsAnalysis: analysisData.indicators_analysis || {},
      
      // Анализ объемов
      volumeAnalysis: analysisData.volume_analysis || {},
      
      // Корреляции индикаторов
      indicatorCorrelations: analysisData.indicator_correlations || {},
      
      // Анализ гэпов
      gapAnalysis: analysisData.gap_analysis || {},
      
      // Психологические уровни
      psychologicalLevels: analysisData.psychological_levels || {},
      
      // Fair Value Gaps
      fairValueGaps: analysisData.fair_value_gaps || [],
      
      // Расширенный анализ Ичимоку
      extendedIchimokuAnalysis: analysisData.extended_ichimoku_analysis || {},
      
      // Волатильность по интервалам
      volatilityByIntervals: analysisData.volatility_by_intervals || {},
      
      // Аномальные свечи
      anomalousCandles: analysisData.anomalous_candles || [],
      
      // Прогноз цены
      pricePrediction: analysisData.price_prediction || {},
      
      // Торговые рекомендации
      recommendations: analysisData.recommendations || {},
      
      // Обратная связь модели
      feedback: analysisData.feedback || {}
    };
  } catch (error) {
    console.error('Ошибка парсинга данных анализа:', error);
    return null;
  }
};

/**
 * Извлекает торговые рекомендации в удобном формате
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив торговых стратегий
 */
export const extractTradingRecommendations = (analysisData) => {
  if (!analysisData?.recommendations?.trading_strategies) {
    return [];
  }

  return analysisData.recommendations.trading_strategies.map((strategy, index) => ({
    id: index + 1,
    strategy: strategy.strategy || 'Не указана',
    signal: determineSignalType(strategy),
    entryPrice: strategy.entry_point?.Price || 0,
    entryDate: strategy.entry_point?.Date || '',
    exitPrice: strategy.exit_point?.Price || 0,
    exitDate: strategy.exit_point?.Date || '',
    stopLoss: strategy.stop_loss || 0,
    takeProfit: strategy.take_profit || 0,
    risk: strategy.risk || 'Не указан',
    profit: strategy.profit || 'Не указан',
    details: strategy.other_details || ''
  }));
};

/**
 * Определяет тип сигнала на основе стратегии
 * @param {Object} strategy - Торговая стратегия
 * @returns {string} Тип сигнала: 'BUY', 'SELL', 'HOLD'
 */
const determineSignalType = (strategy) => {
  const strategyText = (strategy.strategy || '').toLowerCase();
  
  if (strategyText.includes('нисходящ') || strategyText.includes('продаж') || strategyText.includes('короткой')) {
    return 'SELL';
  } else if (strategyText.includes('восходящ') || strategyText.includes('покупк') || strategyText.includes('длинной')) {
    return 'BUY';
  } else if (strategyText.includes('удержан') || strategyText.includes('коррекц')) {
    return 'HOLD';
  }
  
  return 'HOLD'; // По умолчанию
};

/**
 * Извлекает уровни поддержки и сопротивления для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Уровни поддержки и сопротивления
 */
export const extractSupportResistanceLevels = (analysisData) => {
  const supportResistance = analysisData?.supportResistance || {};
  
  return {
    supports: (supportResistance.supports || []).map(level => ({
      price: level.level,
      date: level.date,
      explanation: level.explanation,
      type: 'support'
    })),
    resistances: (supportResistance.resistances || []).map(level => ({
      price: level.level,
      date: level.date,
      explanation: level.explanation,
      type: 'resistance'
    }))
  };
};

/**
 * Извлекает трендовые линии для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив трендовых линий
 */
export const extractTrendLines = (analysisData) => {
  const trendLines = analysisData?.trendLines?.lines || [];
  
  return trendLines.map((line, index) => ({
    id: index + 1,
    type: line.type,
    startPoint: {
      date: line.start_point?.date,
      price: line.start_point?.price
    },
    endPoint: {
      date: line.end_point?.date,
      price: line.end_point?.price
    },
    slopeAngle: line.slope_angle
  }));
};

/**
 * Извлекает уровни Фибоначчи
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Уровни Фибоначчи
 */
export const extractFibonacciLevels = (analysisData) => {
  const fibonacci = analysisData?.fibonacciAnalysis || {};
  
  return {
    localTrend: {
      levels: fibonacci.based_on_local_trend?.levels || {},
      startPoint: fibonacci.based_on_local_trend?.start_point || {},
      endPoint: fibonacci.based_on_local_trend?.end_point || {},
      explanation: fibonacci.based_on_local_trend?.explanation || ''
    },
    globalTrend: {
      levels: fibonacci.based_on_global_trend?.levels || {},
      startPoint: fibonacci.based_on_global_trend?.start_point || {},
      endPoint: fibonacci.based_on_global_trend?.end_point || {},
      explanation: fibonacci.based_on_global_trend?.explanation || ''
    }
  };
};

/**
 * Извлекает ключевые индикаторы для отображения
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Ключевые индикаторы
 */
export const extractKeyIndicators = (analysisData) => {
  const indicators = analysisData?.indicatorsAnalysis || {};
  
  return {
    rsi: {
      value: indicators.RSI?.current_value || 0,
      trend: indicators.RSI?.trend || '',
      comment: indicators.RSI?.comment || ''
    },
    macd: {
      value: indicators.MACD?.current_value || 0,
      signal: indicators.MACD?.signal || 0,
      histogram: indicators.MACD?.histogram || 0,
      trend: indicators.MACD?.trend || '',
      comment: indicators.MACD?.comment || ''
    },
    atr: {
      value: indicators.ATR?.current_value || 0,
      trend: indicators.ATR?.trend || '',
      comment: indicators.ATR?.comment || ''
    },
    adx: {
      value: indicators.ADX?.current_value || 0,
      trend: indicators.ADX?.trend || '',
      comment: indicators.ADX?.comment || ''
    }
  };
};

/**
 * Извлекает прогноз цены
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Прогноз цены
 */
export const extractPricePrediction = (analysisData) => {
  const prediction = analysisData?.pricePrediction || {};
  
  return {
    forecast: prediction.forecast || '',
    virtualCandles: prediction.virtual_candles || []
  };
};

/**
 * Форматирует цену для отображения
 * @param {number} price - Цена
 * @returns {string} Отформатированная цена
 */
export const formatPrice = (price) => {
  if (!price || isNaN(price)) return '0.00';
  return new Intl.NumberFormat('ru-RU', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(price);
};

/**
 * Форматирует дату для отображения
 * @param {string} dateString - Строка даты
 * @returns {string} Отформатированная дата
 */
export const formatDate = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateString;
  }
};

/**
 * Извлекает психологические уровни для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив психологических уровней
 */
export const extractPsychologicalLevels = (analysisData) => {
  const psychLevels = analysisData?.psychologicalLevels?.levels || [];

  return psychLevels.map((level, index) => ({
    id: index + 1,
    level: level.level,
    price: level.level, // Для совместимости с существующим кодом
    date: level.date,
    type: level.type,
    explanation: level.explanation,
    color: level.type === 'Support' ? 'rgba(76, 175, 80, 0.7)' : 'rgba(244, 67, 54, 0.7)' // Зеленый для поддержки, красный для сопротивления
  }));
};

/**
 * Извлекает пивот точки для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Пивот точки
 */
export const extractPivotPoints = (analysisData) => {
  const pivotData = analysisData?.pivotPoints || {};

  if (!pivotData.pivot) return null;

  return {
    pivot: {
      price: pivotData.pivot.price,
      date: pivotData.pivot.date,
      explanation: pivotData.pivot.explanation
    },
    // Дополнительные уровни пивота, если есть
    support1: pivotData.support1 || null,
    support2: pivotData.support2 || null,
    resistance1: pivotData.resistance1 || null,
    resistance2: pivotData.resistance2 || null
  };
};

/**
 * Извлекает незавершенные зоны для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив незавершенных зон
 */
export const extractUnfinishedZones = (analysisData) => {
  const zones = analysisData?.unfinishedZones || [];

  return zones.map((zone, index) => ({
    id: index + 1,
    type: zone.type,
    level: zone.level,
    date: zone.date,
    lineStyle: zone.line_style || 'solid',
    lineColor: zone.line_color || '#888888',
    explanation: zone.explanation
  }));
};

/**
 * Извлекает свечные паттерны для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив свечных паттернов
 */
export const extractCandlestickPatterns = (analysisData) => {
  const patterns = analysisData?.candlestickPatterns || [];

  return patterns.map((pattern, index) => ({
    id: index + 1,
    type: pattern.type,
    date: pattern.date,
    price: pattern.price,
    explanation: pattern.explanation,
    isBearish: pattern.type.toLowerCase().includes('bearish') ||
               pattern.type.toLowerCase().includes('медвежий'),
    color: pattern.type.toLowerCase().includes('bearish') ||
           pattern.type.toLowerCase().includes('медвежий') ?
           'rgba(244, 67, 54, 0.8)' : 'rgba(76, 175, 80, 0.8)'
  }));
};

/**
 * Извлекает структурные преимущества для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив структурных преимуществ
 */
export const extractStructuralEdge = (analysisData) => {
  const edges = analysisData?.structuralEdge || [];

  return edges.map((edge, index) => ({
    id: index + 1,
    type: edge.type,
    date: edge.date,
    price: edge.price,
    explanation: edge.explanation,
    color: '#FF4500' // Оранжево-красный цвет
  }));
};

/**
 * Извлекает анализ дивергенций для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив дивергенций
 */
export const extractDivergenceAnalysis = (analysisData) => {
  const divergences = analysisData?.divergenceAnalysis || [];

  return divergences.map((divergence, index) => ({
    id: index + 1,
    indicator: divergence.indicator,
    type: divergence.type,
    date: divergence.date,
    explanation: divergence.explanation,
    isBearish: divergence.type.toLowerCase().includes('bearish') ||
               divergence.type.toLowerCase().includes('медвежий'),
    color: divergence.type.toLowerCase().includes('bearish') ||
           divergence.type.toLowerCase().includes('медвежий') ?
           'rgba(244, 67, 54, 0.8)' : 'rgba(76, 175, 80, 0.8)'
  }));
};
