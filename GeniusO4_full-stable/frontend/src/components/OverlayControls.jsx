import React from 'react';
import { Eye, EyeOff, TrendingUp, Target, Layers, Activity, Brain, <PERSON>hair, BarChart, Zap, AlertTriangle, BarChart3 } from 'lucide-react';

/**
 * Компонент управления аналитическими наложениями на график
 */
export default function OverlayControls({ overlaySettings, onSettingsChange }) {
  const toggleOverlay = (overlayType) => {
    onSettingsChange({
      ...overlaySettings,
      [overlayType]: !overlaySettings[overlayType]
    });
  };

  const overlayTypes = [
    {
      key: 'showSupportResistance',
      label: 'Поддержка/Сопротивление',
      icon: Target,
      color: 'text-green-400',
      description: 'Горизонтальные уровни поддержки и сопротивления'
    },
    {
      key: 'showTrendLines',
      label: 'Трендовые линии',
      icon: TrendingUp,
      color: 'text-blue-400',
      description: 'Восходящие и нисходящие трендовые линии'
    },
    {
      key: 'showFibonacci',
      label: 'Уровни Фибоначчи',
      icon: Layers,
      color: 'text-yellow-400',
      description: 'Локальные и глобальные уровни коррекции Фибоначчи'
    },
    {
      key: 'showImbalances',
      label: 'Зоны дисбаланса',
      icon: Activity,
      color: 'text-orange-400',
      description: 'Fair Value Gap, Single Print, Vector Candle'
    },
    // ЭТАП 1: Критически важные индикаторы
    {
      key: 'showPsychologicalLevels',
      label: 'Психологические уровни',
      icon: Brain,
      color: 'text-purple-400',
      description: 'Круглые числа и психологически значимые уровни'
    },
    {
      key: 'showPivotPoints',
      label: 'Пивот точки',
      icon: Crosshair,
      color: 'text-yellow-500',
      description: 'Ключевые уровни разворота и пивот точки'
    },
    {
      key: 'showCandlestickPatterns',
      label: 'Свечные паттерны',
      icon: BarChart,
      color: 'text-red-400',
      description: 'Doji, Engulfing, Hammer и другие паттерны'
    },
    // ЭТАП 2: Структурный анализ
    {
      key: 'showUnfinishedZones',
      label: 'Незавершенные зоны',
      icon: Zap,
      color: 'text-cyan-400',
      description: 'Bad Low, Weak High, Poor High зоны'
    },
    {
      key: 'showStructuralEdge',
      label: 'Структурные преимущества',
      icon: AlertTriangle,
      color: 'text-orange-500',
      description: 'Swing Fail и другие структурные сигналы'
    },
    {
      key: 'showDivergenceAnalysis',
      label: 'Анализ дивергенций',
      icon: BarChart3,
      color: 'text-indigo-400',
      description: 'Расхождения между ценой и индикаторами'
    }
  ];

  return (
    <div className="bg-gray-800 rounded-lg p-4 space-y-3">
      <h3 className="text-sm font-medium text-gray-200 mb-3 flex items-center gap-2">
        <Layers className="w-4 h-4" />
        Аналитические наложения
      </h3>
      
      {overlayTypes.map(({ key, label, icon: Icon, color, description }) => (
        <div key={key} className="flex items-center justify-between group">
          <div className="flex items-center gap-3 flex-1">
            <Icon className={`w-4 h-4 ${color}`} />
            <div className="flex-1">
              <div className="text-sm text-gray-200">{label}</div>
              <div className="text-xs text-gray-400">{description}</div>
            </div>
          </div>
          
          <button
            onClick={() => toggleOverlay(key)}
            className={`p-1 rounded transition-colors ${
              overlaySettings[key]
                ? 'text-green-400 hover:text-green-300'
                : 'text-gray-500 hover:text-gray-400'
            }`}
            title={overlaySettings[key] ? 'Скрыть' : 'Показать'}
          >
            {overlaySettings[key] ? (
              <Eye className="w-4 h-4" />
            ) : (
              <EyeOff className="w-4 h-4" />
            )}
          </button>
        </div>
      ))}
      
      <div className="pt-2 border-t border-gray-700">
        <div className="flex gap-2">
          <button
            onClick={() => onSettingsChange({
              showSupportResistance: true,
              showTrendLines: true,
              showFibonacci: true,
              showImbalances: true,
              showPsychologicalLevels: true,
              showPivotPoints: true,
              showCandlestickPatterns: true,
              showUnfinishedZones: true,
              showStructuralEdge: true,
              showDivergenceAnalysis: true
            })}
            className="flex-1 px-3 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
          >
            Показать все
          </button>
          <button
            onClick={() => onSettingsChange({
              showSupportResistance: false,
              showTrendLines: false,
              showFibonacci: false,
              showImbalances: false,
              showPsychologicalLevels: false,
              showPivotPoints: false,
              showCandlestickPatterns: false,
              showUnfinishedZones: false,
              showStructuralEdge: false,
              showDivergenceAnalysis: false
            })}
            className="flex-1 px-3 py-1 text-xs bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
          >
            Скрыть все
          </button>
        </div>
      </div>
    </div>
  );
}
