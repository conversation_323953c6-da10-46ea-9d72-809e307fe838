import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px', 
          backgroundColor: '#2a2a2a', 
          color: 'white', 
          borderRadius: '8px',
          margin: '20px'
        }}>
          <h2 style={{ color: '#ff6b6b', marginBottom: '15px' }}>🚨 Ошибка компонента</h2>
          <details style={{ whiteSpace: 'pre-wrap', fontSize: '14px' }}>
            <summary style={{ cursor: 'pointer', marginBottom: '10px' }}>
              Показать детали ошибки
            </summary>
            <div style={{ backgroundColor: '#1a1a1a', padding: '10px', borderRadius: '4px' }}>
              <strong>Ошибка:</strong>
              <pre style={{ color: '#ff6b6b' }}>{this.state.error && this.state.error.toString()}</pre>
              
              <strong>Stack trace:</strong>
              <pre style={{ color: '#ffa500', fontSize: '12px' }}>
                {this.state.errorInfo && this.state.errorInfo.componentStack}
              </pre>
            </div>
          </details>
          <button 
            onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
            style={{
              marginTop: '15px',
              padding: '8px 16px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Попробовать снова
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
